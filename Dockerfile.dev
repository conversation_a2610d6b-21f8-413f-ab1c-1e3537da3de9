# Multi-stage Dockerfile for development and production

# Base stage with common dependencies
FROM node:20-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    wget \
    curl \
    git \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Copy package files for dependency installation
COPY package*.json ./

# Development stage
FROM base AS development

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Create storage directory with proper permissions
RUN mkdir -p storage && chown -R node:node storage

# Switch to non-root user
USER node

# Expose application port and debug port
EXPOSE 3000 9229

# Default command for development (can be overridden)
CMD ["npm", "run", "start:dev"]

